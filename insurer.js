import { configDotenv } from "dotenv";
import axios from "axios";
import pkg from "pg";
import fs from "fs";
import path from "path";
const { Pool } = pkg;

// Initialize environment variables
configDotenv();

// Constants
const STRAPI_BASE_URL = process.env.STRAPI_BASE_URL;
const STRAPI_TOKEN = process.env.TOKEN;

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), "logs");
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Create log file with timestamp
const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
const logFile = path.join(logsDir, `variant-data-migration-${timestamp}.txt`);

// Logging function that writes to both console and file
function logMessage(message, type = "INFO") {
  const timestampStr = new Date().toISOString();
  const logEntry = `[${timestampStr}] [${type}] ${message}`;

  // Write to console
  if (type === "ERROR") {
    console.error(message);
  } else if (type === "WARN") {
    console.warn(message);
  } else {
    console.log(message);
  }

  // Write to file
  try {
    fs.appendFileSync(logFile, logEntry + "\n", "utf8");
  } catch (error) {
    console.error(`Failed to write to log file: ${error.message}`);
  }
}

// Enhanced logging functions
const logger = {
  info: (message) => logMessage(message, "INFO"),
  warn: (message) => logMessage(message, "WARN"),
  error: (message) => logMessage(message, "ERROR"),
  success: (message) => logMessage(message, "SUCCESS"),
};

// PostgreSQL configuration
const dbConfig = {
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT || 5432,
  ssl: process.env.DB_SSL === "true" ? { rejectUnauthorized: false } : false,
};

// Create PostgreSQL pool
const pool = new Pool(dbConfig);

// Axios instance with common config
const strapiAPI = axios.create({
  baseURL: STRAPI_BASE_URL,
  headers: {
    Authorization: `Bearer ${STRAPI_TOKEN}`,
  },
});

// Function to fetch complete variant data from Strapi API
async function fetchCompleteInsurerData(insurerSlug) {
  try {
    logger.info(`Fetching complete data for insurer: ${insurerSlug}`);

    // Complete API call with all populate fields from the original script
    const populateQuery = `
      filters[slug][$eq]=${insurerSlug}&
      populate[faqs][fields][0]=question&
      populate[faqs][fields][1]=ans&
      populate[claimSettlement][fields][0]=title&
      populate[claimSettlement][populate][cashless][fields][0]=title&
      populate[claimSettlement][populate][cashless][populate][steps][fields][0]=title&
      populate[claimSettlement][populate][cashless][populate][steps][fields][1]=description&
      populate[claimSettlement][populate][reimbursement][fields][0]=title&
      populate[claimSettlement][populate][reimbursement][populate][steps][fields][0]=title&
      populate[claimSettlement][populate][reimbursement][populate][steps][fields][1]=description&
      populate[hero][fields][0]=title&
      populate[seo][fields][0]=metaTitle&
      populate[seo][fields][1]=metaDescription&
      populate[seo][fields][2]=keyword&
      populate[seo][fields][3]=preventIndexing&
      populate[seo][fields][4]=source&
      populate[policyGuide][fields][0]=title&
      populate[policyGuide][populate][guidePoint][fields][0]=title&
      populate[policyGuide][populate][guidePoint][fields][1]=description&
      populate[logo][fields][0]=url&
      populate[pros][fields][0]=title&
      populate[pros][fields][1]=description&
      populate[cons][fields][0]=title&
      populate[cons][fields][1]=description&
      populate[kycDocs][fields][0]=name&
      populate[renewalSteps][fields][0]=title&
      populate[renewalSteps][populate][onlineRenewal][fields][0]=title&
      populate[renewalSteps][populate][onlineRenewal][populate][steps][fields][0]=title&
      populate[renewalSteps][populate][onlineRenewal][populate][steps][fields][1]=description&
      populate[renewalSteps][populate][offlineRenewal][fields][0]=title&
      populate[renewalSteps][populate][offlineRenewal][populate][steps][fields][0]=title&
      populate[renewalSteps][populate][offlineRenewal][populate][steps][fields][1]=description&
      populate[renewalKeyPoints][fields][0]=point&
      populate[customerSupport][fields][0]=email&
      populate[customerSupport][fields][1]=contactNumber&
      populate[ratings][fields][0]=solvency&
      populate[ratings][fields][1]=icr&
      populate[ratings][fields][2]=growth&
      populate[ratings][fields][3]=aum&
      populate[statistics][populate][grossDirectPremium][fields][0]=industry&
      populate[statistics][populate][grossDirectPremium][fields][1]=company&
      populate[statistics][populate][grossDirectPremium][fields][2]=description&
      populate[statistics][populate][icr][fields][0]=industry&
      populate[statistics][populate][icr][fields][1]=company&
      populate[statistics][populate][icr][fields][2]=description&
      populate[statistics][populate][premiumUnderwritten][fields][0]=industry&
      populate[statistics][populate][premiumUnderwritten][fields][1]=company&
      populate[statistics][populate][premiumUnderwritten][fields][2]=description&
      populate[statistics][populate][solvencyRatio][fields][0]=industry&
      populate[statistics][populate][solvencyRatio][fields][1]=company&
      populate[statistics][populate][solvencyRatio][fields][2]=description
    `.replace(/\s+/g, "");

    const response = await strapiAPI.get(
      `/api/companies?${populateQuery}`
    );

    if (response.data.data && response.data.data.length > 0) {
      const insurerData = response.data.data[0].attributes;
      console.log(JSON.stringify(insurerData, null, 2));
      logger.info(`Successfully fetched data for insurer: ${insurerSlug}`);
      return insurerData;
    } else {
      logger.warn(`No data found for insurer: ${insurerSlug}`);
      return null;
    }
  } catch (error) {
    logger.error(
      `Error fetching data for insurer ${insurerSlug}: ${error.message}`
    );
    return null;
  }
}

// fetchCompleteInsurerData("aditya-birla-health-insurance");

async function generateNewId() {
  try {
    const response = await fetch(
      "https://idgen.corp.non-prod.oneassure.in/generate",
      {
        headers: {
          "sec-ch-ua":
            '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          "sec-ch-ua-mobile": "?1",
          "sec-ch-ua-platform": '"Android"',
          Referer: "https://idgen.corp.non-prod.oneassure.in/",
          "Referrer-Policy": "strict-origin-when-cross-origin",
        },
        body: null,
        method: "GET",
      }
    );

    if (!response.ok) {
      throw new Error(`ID generation failed: ${response.statusText}`);
    }

    const data = await response.json();
    return data.id;
  } catch (error) {
    logger.error(`❌ Failed to generate new ID: ${error.message}`);
    throw error;
  }
}

// Function to get insurer ID from slug
async function getInsurerIdFromSlug(client, insurerSlug) {
  try {
    const query = `SELECT id FROM health.insurers WHERE slug = $1`;
    const result = await client.query(query, [insurerSlug]);

    if (result.rows.length > 0) {
      return result.rows[0].id;
    } else {
      logger.warn(`Insurer with slug '${insurerSlug}' not found in database`);
      return null;
    }
  } catch (error) {
    logger.error(
      `Error getting insurer ID for slug ${insurerSlug}: ${error.message}`
    );
    return null;
  }
}

// // Function to insert static content
async function insertStaticContent(client, insurerId, data) {
  try {
    // Check if static content already exists
    const checkQuery = `SELECT (id, hero_title, verdict, legacy, kyc_docs, customer_support_email, customer_support_number, renewal_key_points) FROM site.insurer_static_content WHERE insurer_id = $1`;
    const existingResult = await client.query(checkQuery, [insurerId]);
    console.log(existingResult);
    if (existingResult.rows.length > 0) {
      logger.info(
        `Static content already exists for insurer ${insurerId}, skipping...`
      );
      const query = `UPDATE site.insurer_static_content SET hero_title = $1, verdict = $2, legacy = $3, kyc_docs = $4, customer_support_email = $5, customer_support_number = $6, renewal_key_points = $7 WHERE insurer_id = $8`;
      const values = [
        data.hero?.title || "",
        data?.verdict || "",
        data?.legecy || "",
        data?.kycDocs.map((doc) => doc.name) || [],
        data?.customerSupport?.email || "",
        data?.customerSupport?.contactNumber || "",
        data?.renewalKeyPoints.map((point) => point.point) || [],
        insurerId,
      ];
      await client.query(query, values);
      logger.success(
        `✅ Updated static content for insurer ${insurerId}`
      );
      return true;
    }

    const query = `
      INSERT INTO site.insurer_static_content 
      (id, insurer_id, hero_title, verdict, legacy, kyc_docs, customer_support_email, customer_support_number, renewal_key_points, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
    `;

    const values = [
      await generateNewId(),
      insurerId,
      data?.hero.title || "",
      data?.verdict || "",
      data?.legecy || "",
      data?.kycDocs.map((doc) => doc.name) || [],
      data?.customerSupport?.email || "",
      data?.customerSupport?.contactNumber || "",
      data?.renewalKeyPoints.map((point) => point.point) || [],
    ];
    console.log(query, values);
    await client.query(query, values);
    logger.success(`✅ Inserted static content for insurer ${insurerId}`);
    return true;
  } catch (error) {
    console.log(error);
    logger.error(
      `❌ Failed to insert static content for insurer ${insurerId}: ${error.message}`
    );
    return false;
  }
}

// // Function to insert FAQs
async function insertFAQs(client, insurerId, faqs) {
  if (!faqs || !Array.isArray(faqs) || faqs.length === 0) {
    logger.info(`No FAQs to insert for insurer ${insurerId}`);
    return true;
  }

  try {
    // Clear existing FAQs
    await client.query(
      `DELETE FROM site.insurer_faqs WHERE insurer_id = $1`,
      [insurerId]
    );

    for (const faq of faqs) {
      const query = `
        INSERT INTO site.insurer_faqs 
        (id, question, answer, insurer_id, faq_type, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
      `;

      const values = [
        await generateNewId(),
        faq.question || "",
        faq.ans || "",
        insurerId,
        "general"
      ];

      await client.query(query, values);
    }

    logger.success(
      `✅ Inserted ${faqs.length} FAQs for insurer ${insurerId}`
    );
    return true;
  } catch (error) {
    logger.error(
      `❌ Failed to insert FAQs for insurer ${insurerId}: ${error.message}`
    );
    return false;
  }
}

// Function to clear existing claim settlement data (respecting foreign key constraints)
async function clearExistingClaimSettlementData(client, insurerId) {
  try {
    // Delete in correct order: steps -> types -> settlement (child to parent)

    // First, delete all steps for this insurer's claim settlement
    await client.query(`
      DELETE FROM site.insurer_claim_settlement_steps
      WHERE insurer_claim_settlement_type_id IN (
        SELECT ict.id
        FROM site.insurer_claim_settlement_type ict
        JOIN site.insurer_claim_settlement ics ON ict.insurer_claim_settlement_id = ics.id
        WHERE ics.insurer_id = $1
      )
    `, [insurerId]);

    // Then, delete all claim settlement types for this insurer
    await client.query(`
      DELETE FROM site.insurer_claim_settlement_type
      WHERE insurer_claim_settlement_id IN (
        SELECT id FROM site.insurer_claim_settlement WHERE insurer_id = $1
      )
    `, [insurerId]);

    // Finally, delete the main claim settlement record
    await client.query(`DELETE FROM site.insurer_claim_settlement WHERE insurer_id = $1`, [insurerId]);

    logger.info(`✅ Cleared existing claim settlement data for insurer ${insurerId}`);
    return true;
  } catch (error) {
    logger.error(`❌ Failed to clear existing claim settlement data for insurer ${insurerId}: ${error.message}`);
    return false;
  }
}

async function insertInsurerClaimSettlement(client, insurerId, claimSettlement) {
  if (!claimSettlement) {
    logger.info(`No claim settlement to insert for insurer ${insurerId}`);
    return null;
  }

  try {
    const query = `
      INSERT INTO site.insurer_claim_settlement
      (id, insurer_id, title, created_at, updated_at)
      VALUES ($1, $2, $3, NOW(), NOW())
      RETURNING id
    `;

    const values = [
      await generateNewId(),
      insurerId,
      claimSettlement.title || "",
    ];

    const result = await client.query(query, values);
    const claimSettlementId = result.rows[0].id;

    logger.success(`✅ Inserted claim settlement for insurer ${insurerId}`);
    return claimSettlementId;
  } catch (error) {
    logger.error(
      `❌ Failed to insert claim settlement for insurer ${insurerId}: ${error.message}`
    );
    return null;
  }
}

// Function to insert insurer claim settlement types
async function insertInsurerClaimSettlementTypes(client, claimSettlementId, claimSettlement) {
  if (!claimSettlement || (!claimSettlement.cashless && !claimSettlement.reimbursement)) {
    logger.info(`No claim settlement types to insert for claim settlement ${claimSettlementId}`);
    return {};
  }

  try {
    const insertedTypes = {};

    // Insert cashless claim type if exists
    if (claimSettlement.cashless) {
      const cashlessQuery = `
        INSERT INTO site.insurer_claim_settlement_type
        (id, insurer_claim_settlement_id, title, type, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
        RETURNING id
      `;

      const cashlessValues = [
        await generateNewId(),
        claimSettlementId,
        claimSettlement.cashless.data.attributes.title || "",
        'cashless claim'
      ];

      const cashlessResult = await client.query(cashlessQuery, cashlessValues);
      insertedTypes.cashless = cashlessResult.rows[0].id;
      logger.success(`✅ Inserted cashless claim type for claim settlement ${claimSettlementId}`);
    }

    // Insert reimbursement claim type if exists
    if (claimSettlement.reimbursement) {
      const reimbursementQuery = `
        INSERT INTO site.insurer_claim_settlement_type
        (id, insurer_claim_settlement_id, title, type, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
        RETURNING id
      `;

      const reimbursementValues = [
        await generateNewId(),
        claimSettlementId,
        claimSettlement.reimbursement.data.attributes.title || "",
        'reimbursement claim'
      ];

      const reimbursementResult = await client.query(reimbursementQuery, reimbursementValues);
      insertedTypes.reimbursement = reimbursementResult.rows[0].id;
      logger.success(`✅ Inserted reimbursement claim type for claim settlement ${claimSettlementId}`);
    }

    return insertedTypes;
  } catch (error) {
    logger.error(
      `❌ Failed to insert claim settlement types for claim settlement ${claimSettlementId}: ${error.message}`
    );
    return {};
  }
}

// Function to insert insurer claim settlement steps
async function insertInsurerClaimSettlementSteps(client, claimSettlementTypeId, steps, stepType) {
  if (!steps || !Array.isArray(steps) || steps.length === 0) {
    logger.info(`No ${stepType} steps to insert for claim settlement type ${claimSettlementTypeId}`);
    return true;
  }

  try {
    for (const step of steps) {
      const query = `
        INSERT INTO site.insurer_claim_settlement_steps
        (id, insurer_claim_settlement_type_id, title, description, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
      `;

      const values = [
        await generateNewId(),
        claimSettlementTypeId,
        step.title || "",
        step.description || "",
      ];

      await client.query(query, values);
    }

    logger.success(
      `✅ Inserted ${steps.length} ${stepType} steps for claim settlement type ${claimSettlementTypeId}`
    );
    return true;
  } catch (error) {
    logger.error(
      `❌ Failed to insert ${stepType} steps for claim settlement type ${claimSettlementTypeId}: ${error.message}`
    );
    return false;
  }
}

// Main function to insert complete claim settlement data
async function insertCompleteClaimSettlement(client, insurerId, claimSettlement) {
  if (!claimSettlement) {
    logger.info(`No claim settlement data to insert for insurer ${insurerId}`);
    return true;
  }

  try {
    // Clear existing claim settlement data (respecting foreign key constraints)
    const cleared = await clearExistingClaimSettlementData(client, insurerId);
    if (!cleared) {
      return false;
    }

    // Insert main claim settlement record
    const claimSettlementId = await insertInsurerClaimSettlement(client, insurerId, claimSettlement);
    if (!claimSettlementId) {
      return false;
    }

    // Insert claim settlement types (cashless and reimbursement)
    const insertedTypes = await insertInsurerClaimSettlementTypes(client, claimSettlementId, claimSettlement);

    // Insert steps for cashless claim type
    if (insertedTypes.cashless && claimSettlement.cashless?.data?.attributes?.steps) {
      await insertInsurerClaimSettlementSteps(
        client,
        insertedTypes.cashless,
        claimSettlement.cashless.data.attributes.steps,
        'cashless'
      );
    }

    // Insert steps for reimbursement claim type
    if (insertedTypes.reimbursement && claimSettlement.reimbursement?.data?.attributes?.steps) {
      await insertInsurerClaimSettlementSteps(
        client,
        insertedTypes.reimbursement,
        claimSettlement.reimbursement.data.attributes.steps,
        'reimbursement'
      );
    }

    logger.success(`✅ Successfully inserted complete claim settlement data for insurer ${insurerId}`);
    return true;
  } catch (error) {
    logger.error(
      `❌ Failed to insert complete claim settlement data for insurer ${insurerId}: ${error.message}`
    );
    return false;
  }
}

// Function to insert insurer SEO content
async function insertInsurerSEO(client, insurerId, seo) {
  if (!seo) {
    logger.info(`No SEO content to insert for insurer ${insurerId}`);
    return true;
  }

  try {
    // Check if SEO content already exists
    const checkQuery = `SELECT id FROM site.insurer_seo WHERE insurer_id = $1`;
    const existingResult = await client.query(checkQuery, [insurerId]);

    if (existingResult.rows.length > 0) {
      // Update existing SEO content
      const updateQuery = `
        UPDATE site.insurer_seo
        SET meta_title = $1, meta_description = $2, meta_keyword = $3, prevent_indexing = $4, source = $5, updated_at = NOW()
        WHERE insurer_id = $6
      `;

      const values = [
        seo.metaTitle || "",
        seo.metaDescription || "",
        seo.keyword || "",
        seo.preventIndexing || false,
        seo.source || "none",
        insurerId,
      ];

      await client.query(updateQuery, values);
      logger.success(`✅ Updated SEO content for insurer ${insurerId}`);
    } else {
      // Insert new SEO content
      const insertQuery = `
        INSERT INTO site.insurer_seo
        (id, insurer_id, meta_title, meta_description, meta_keyword, prevent_indexing, source, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
      `;

      const values = [
        await generateNewId(),
        insurerId,
        seo.metaTitle || "",
        seo.metaDescription || "",
        seo.keyword || "",
        seo.preventIndexing || false,
        seo.source || "none",
      ];

      await client.query(insertQuery, values);
      logger.success(`✅ Inserted SEO content for insurer ${insurerId}`);
    }

    return true;
  } catch (error) {
    logger.error(
      `❌ Failed to insert SEO content for insurer ${insurerId}: ${error.message}`
    );
    return false;
  }
}

// Function to clear existing policy guide data (respecting foreign key constraints)
async function clearExistingPolicyGuideData(client, insurerId) {
  try {
    // Delete in correct order: points -> guides (child to parent)

    // First, delete all policy guide points for this insurer
    await client.query(`
      DELETE FROM site.insurer_policy_guide_points
      WHERE insurer_policy_guide_id IN (
        SELECT id FROM site.insurer_policy_guides WHERE insurer_id = $1
      )
    `, [insurerId]);

    // Then, delete all policy guides for this insurer
    await client.query(`DELETE FROM site.insurer_policy_guides WHERE insurer_id = $1`, [insurerId]);

    logger.info(`✅ Cleared existing policy guide data for insurer ${insurerId}`);
    return true;
  } catch (error) {
    logger.error(`❌ Failed to clear existing policy guide data for insurer ${insurerId}: ${error.message}`);
    return false;
  }
}

// Function to insert insurer policy guide
async function insertInsurerPolicyGuide(client, insurerId, policyGuide) {
  if (!policyGuide) {
    logger.info(`No policy guide to insert for insurer ${insurerId}`);
    return null;
  }

  try {
    const query = `
      INSERT INTO site.insurer_policy_guides
      (id, insurer_id, title, created_at, updated_at)
      VALUES ($1, $2, $3, NOW(), NOW())
      RETURNING id
    `;

    const values = [
      await generateNewId(),
      insurerId,
      policyGuide.title || "",
    ];

    const result = await client.query(query, values);
    const policyGuideId = result.rows[0].id;

    logger.success(`✅ Inserted policy guide for insurer ${insurerId}`);
    return policyGuideId;
  } catch (error) {
    logger.error(
      `❌ Failed to insert policy guide for insurer ${insurerId}: ${error.message}`
    );
    return null;
  }
}

// Function to insert insurer policy guide points
async function insertInsurerPolicyGuidePoints(client, policyGuideId, guidePoints) {
  if (!guidePoints || !Array.isArray(guidePoints) || guidePoints.length === 0) {
    logger.info(`No policy guide points to insert for policy guide ${policyGuideId}`);
    return true;
  }

  try {
    for (const point of guidePoints) {
      const query = `
        INSERT INTO site.insurer_policy_guide_points
        (id, insurer_policy_guide_id, title, description, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
      `;

      const values = [
        await generateNewId(),
        policyGuideId,
        point.title || "",
        point.description || "",
      ];

      await client.query(query, values);
    }

    logger.success(
      `✅ Inserted ${guidePoints.length} policy guide points for policy guide ${policyGuideId}`
    );
    return true;
  } catch (error) {
    logger.error(
      `❌ Failed to insert policy guide points for policy guide ${policyGuideId}: ${error.message}`
    );
    return false;
  }
}

// Main function to insert complete policy guide data
async function insertCompletePolicyGuide(client, insurerId, policyGuide) {
  if (!policyGuide) {
    logger.info(`No policy guide data to insert for insurer ${insurerId}`);
    return true;
  }

  try {
    // Clear existing policy guide data (respecting foreign key constraints)
    const cleared = await clearExistingPolicyGuideData(client, insurerId);
    if (!cleared) {
      return false;
    }

    // Insert main policy guide record
    const policyGuideId = await insertInsurerPolicyGuide(client, insurerId, policyGuide);
    if (!policyGuideId) {
      return false;
    }

    // Insert policy guide points
    if (policyGuide.guidePoint && Array.isArray(policyGuide.guidePoint)) {
      await insertInsurerPolicyGuidePoints(client, policyGuideId, policyGuide.guidePoint);
    }

    logger.success(`✅ Successfully inserted complete policy guide data for insurer ${insurerId}`);
    return true;
  } catch (error) {
    logger.error(
      `❌ Failed to insert complete policy guide data for insurer ${insurerId}: ${error.message}`
    );
    return false;
  }
}

// Function to insert insurer pros and cons
async function insertInsurerProsCons(client, insurerId, prosData, consData) {
  try {
    // Clear existing pros and cons data
    await client.query(`DELETE FROM site.insurer_pros_cons WHERE insurer_id = $1`, [insurerId]);

    let totalInserted = 0;

    // Insert pros
    if (prosData && Array.isArray(prosData) && prosData.length > 0) {
      for (const pro of prosData) {
        const query = `
          INSERT INTO site.insurer_pros_cons
          (id, insurer_id, title, description, type, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        `;

        const values = [
          await generateNewId(),
          insurerId,
          pro.title || "",
          pro.description || "",
          'pro'
        ];

        await client.query(query, values);
        totalInserted++;
      }
      logger.success(`✅ Inserted ${prosData.length} pros for insurer ${insurerId}`);
    }

    // Insert cons
    if (consData && Array.isArray(consData) && consData.length > 0) {
      for (const con of consData) {
        const query = `
          INSERT INTO site.insurer_pros_cons
          (id, insurer_id, title, description, type, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        `;

        const values = [
          await generateNewId(),
          insurerId,
          con.title || "",
          con.description || "",
          'con'
        ];

        await client.query(query, values);
        totalInserted++;
      }
      logger.success(`✅ Inserted ${consData.length} cons for insurer ${insurerId}`);
    }

    if (totalInserted === 0) {
      logger.info(`No pros or cons to insert for insurer ${insurerId}`);
    } else {
      logger.success(`✅ Successfully inserted ${totalInserted} pros/cons for insurer ${insurerId}`);
    }

    return true;
  } catch (error) {
    logger.error(
      `❌ Failed to insert pros/cons for insurer ${insurerId}: ${error.message}`
    );
    return false;
  }
}

// // Main function to migrate insurer data
async function migrateInsurerData(client, insurerSlug) {
  try {
    logger.info(`🚀 Starting migration for insurer: ${insurerSlug}`);

    // Get insurer ID from slug
    const insurerId = await getInsurerIdFromSlug(client, insurerSlug);
    if (!insurerId) {
      logger.error(`❌ Could not find insurer ID for slug: ${insurerSlug}`);
      return false;
    }

    // Fetch complete data from Strapi
    const data = await fetchCompleteInsurerData(insurerSlug);
    if (!data) {
      logger.error(`❌ Could not fetch data for insurer: ${insurerSlug}`);
      return false;
    }

    // Begin transaction
    await client.query("BEGIN");

    try {
      // Insert data into various tables
      await insertStaticContent(client, insurerId, data);
      await insertFAQs(client, insurerId, data.faqs);
      await insertCompleteClaimSettlement(client, insurerId, data.claimSettlement);
      await insertInsurerSEO(client, insurerId, data.seo);
      await insertCompletePolicyGuide(client, insurerId, data.policyGuide);
      // Commit transaction
      await client.query("COMMIT");
      logger.success(
        `✅ Successfully migrated all data for insurer: ${insurerSlug}`
      );
      return true;
    } catch (error) {
      // Rollback transaction on error
      await client.query("ROLLBACK");
      throw error;
    }
  } catch (error) {
    logger.error(
      `❌ Failed to migrate insurer ${insurerSlug}: ${error.message}`
    );
    return false;
  }
}

// Function to migrate specific insurer by slug
async function migrateSpecificInsurer(insurerSlug) {
  const client = await pool.connect();

  try {
    logger.info(`🎯 Migrating specific variant: ${insurerSlug}`);
    const success = await migrateInsurerData(client, insurerSlug);

    if (success) {
      logger.success(`✅ Successfully migrated variant: ${insurerSlug}`);
    } else {
      logger.error(`❌ Failed to migrate variant: ${insurerSlug}`);
    }
  } catch (error) {
    logger.error(`❌ Error migrating variant ${insurerSlug}: ${error.message}`);
  } finally {
    client.release();
  }
}

// // Function to migrate all variants
// async function migrateAllVariants() {
//   const client = await pool.connect();
//   const variants = await client.query(
//     `SELECT variant_slug FROM health.product_variants WHERE variant_slug IS NOT NULL`
//   );
//   for (const variant of variants.rows) {
//     await migrateVariantData(client, variant.variant_slug);
//   }
// }
// // Function to validate environment variables
// function validateEnvironment() {
//   const requiredVars = [
//     "STRAPI_BASE_URL",
//     "TOKEN",
//     "DB_USER",
//     "DB_HOST",
//     "DB_NAME",
//     "DB_PASSWORD",
//   ];

//   const missing = requiredVars.filter((varName) => !process.env[varName]);

//   if (missing.length > 0) {
//     logger.error("❌ Missing required environment variables:");
//     missing.forEach((varName) => logger.error(`   - ${varName}`));
//     process.exit(1);
//   }
// }

// // Function to test database connection
async function testDatabaseConnection() {
  try {
    const client = await pool.connect();

    // Test connection to the health.product_variants table
    const testQuery = `SELECT COUNT(*) FROM health.insurers WHERE slug IS NOT NULL`;
    const result = await client.query(testQuery);

    logger.success("✅ Database connection successful");
    logger.info(
      `📊 Found ${result.rows[0].count} insurers with slugs in database`
    );

    client.release();
  } catch (error) {
    console.log(error);
    logger.error("❌ Database connection failed:", error);
    process.exit(1);
  }
}

// // Main function
// async function main() {
//   logger.info("🏥 Variant Data Migration Tool");
//   logger.info("=====================================");
//   logger.info(`Migration started at: ${new Date().toISOString()}`);
//   logger.info(`Log file: ${logFile}`);
//   logger.info(`Strapi Base URL: ${STRAPI_BASE_URL}`);
//   logger.info(`Database Host: ${dbConfig.host}`);
//   logger.info(`Database Name: ${dbConfig.database}`);
//   logger.info("=====================================");

//   validateEnvironment();
//   await testDatabaseConnection();

//   // Get variant slug from command line argument
//   const variantSlug = process.argv[2];

//   if (!variantSlug) {
//     logger.info(`🎯 Migrating data for all variants`);
//     await migrateAllVariants();
//   } else {
//     logger.info(`🎯 Migrating data for variant slug: ${variantSlug}`);
//     await migrateSpecificVariant(variantSlug);
//   }

//   // Write final summary to log
//   const endTime = new Date().toISOString();
//   logger.info("=====================================");
//   logger.info("MIGRATION COMPLETED");
//   logger.info(`End time: ${endTime}`);
//   logger.info(`Log file saved to: ${logFile}`);
//   logger.info("=====================================");
// }

// // Handle graceful shutdown
// process.on("SIGINT", () => {
//   logger.warn("\n⚠️  Migration interrupted by user");
//   pool.end();
//   process.exit(0);
// });

// process.on("unhandledRejection", (error) => {
//   logger.error("❌ Unhandled promise rejection:", error);
//   pool.end();
//   process.exit(1);
// });

// // Close pool on exit
// process.on("exit", () => {
//   pool.end();
// });

// // Run the migration
// main().catch((error) => {
//   logger.error("❌ Migration failed:", error);
//   pool.end();
//   process.exit(1);
// });


testDatabaseConnection();
migrateSpecificInsurer("aditya-birla-health-insurance");
