import { configDotenv } from "dotenv";
import axios from "axios";
import pkg from "pg";
import fs from "fs";
import path from "path";
const { Pool } = pkg;

// Initialize environment variables
configDotenv();

// Constants
const STRAPI_BASE_URL = process.env.STRAPI_BASE_URL;
const STRAPI_TOKEN = process.env.TOKEN;

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), "logs");
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Create log file with timestamp
const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
const logFile = path.join(logsDir, `variant-data-migration-${timestamp}.txt`);

// Logging function that writes to both console and file
function logMessage(message, type = "INFO") {
  const timestampStr = new Date().toISOString();
  const logEntry = `[${timestampStr}] [${type}] ${message}`;

  // Write to console
  if (type === "ERROR") {
    console.error(message);
  } else if (type === "WARN") {
    console.warn(message);
  } else {
    console.log(message);
  }

  // Write to file
  try {
    fs.appendFileSync(logFile, logEntry + "\n", "utf8");
  } catch (error) {
    console.error(`Failed to write to log file: ${error.message}`);
  }
}

// Enhanced logging functions
const logger = {
  info: (message) => logMessage(message, "INFO"),
  warn: (message) => logMessage(message, "WARN"),
  error: (message) => logMessage(message, "ERROR"),
  success: (message) => logMessage(message, "SUCCESS"),
};

// PostgreSQL configuration
const dbConfig = {
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT || 5432,
  ssl: process.env.DB_SSL === "true" ? { rejectUnauthorized: false } : false,
};

// Create PostgreSQL pool
const pool = new Pool(dbConfig);

// Axios instance with common config
const strapiAPI = axios.create({
  baseURL: STRAPI_BASE_URL,
  headers: {
    Authorization: `Bearer ${STRAPI_TOKEN}`,
  },
});

// Function to fetch complete variant data from Strapi API
async function fetchCompleteVariantData(variantSlug) {
  try {
    logger.info(`Fetching complete data for variant: ${variantSlug}`);

    // Complete API call with all populate fields from the original script
    const populateQuery = `
      filters[slug][$eq]=${variantSlug}&
      populate[company][populate][fields][0]=name&
      populate[company][populate][fields][1]=slug&
      populate[company][populate][logo][fields][0]=url&
      populate[faqs][fields][0]=question&
      populate[faqs][fields][1]=ans&
      populate[whyOneAssure][fields][0]=title&
      populate[whyOneAssure][fields][1]=description&
      populate[features][fields][0]=title&
      populate[features][fields][1]=description&
      populate[features][populate][listedFeatures][fields][0]=feature&
      populate[blogs][populate][fields][0]=Title&
      populate[blogs][populate][fields][1]=slug&
      populate[blogs][populate][fields][2]=subtitle&
      populate[blogs][populate][category][fields][0]=slug&
      populate[blogs][populate][Thumbnail][fields][0]=url&
      populate[exclusions][fields][0]=exclusion&
      populate[hero][fields][0]=title&
      populate[hero][populate][claimSettlementRatio][fields][0]=value&
      populate[hero][populate][claimSettlementRatio][fields][1]=description&
      populate[hero][populate][networkHospitals][fields][0]=value&
      populate[hero][populate][networkHospitals][fields][1]=description&
      populate[hero][populate][networkHospitals][fields][2]=networkHospitalsURL&
      populate[policyDocs][fields][0]=label&
      populate[policyDocs][populate][document][fields][0]=url&
      populate[highlightedFeatures][fields][0]=title&
      populate[highlightedFeatures][fields][1]=description&
      populate[highlightedFeatures][populate][icon][fields][0]=url&
      populate[addOns][fields][0]=title&
      populate[addOns][fields][1]=description&
      populate[variants][fields][0]=title&
      populate[variants][populate][relatedVariant][fields][0]=slug&
      populate[variants][populate][relatedVariant][fields][1]=name&
      populate[variants][populate][relatedVariant][populate][company][fields][0]=slug&
      populate[variants][populate][relatedVariant][populate][company][fields][1]=name&
      populate[variants][populate][relatedVariant][populate][company][populate][logo][fields][0]=url&
      populate[variants][populate][features][fields][0]=feature&
      populate[seo][fields][0]=metaTitle&
      populate[seo][fields][1]=metaDescription&
      populate[seo][fields][2]=keyword&
      populate[seo][fields][3]=preventIndexing&
      populate[seo][fields][4]=source&
      populate[rating][populate][coverage][populate][fields][0]=title&
      populate[rating][populate][coverage][populate][fields][1]=score&
      populate[rating][populate][coverage][populate][fields][2]=maxScore&
      populate[rating][populate][claimSettlement][populate][fields][0]=title&
      populate[rating][populate][claimSettlement][populate][fields][1]=score&
      populate[rating][populate][claimSettlement][populate][fields][2]=maxScore&
      populate[rating][populate][hospitalNetwork][populate][fields][0]=title&
      populate[rating][populate][hospitalNetwork][populate][fields][1]=score&
      populate[rating][populate][hospitalNetwork][populate][fields][2]=maxScore&
      populate[rating][populate][coPayment][populate][fields][0]=title&
      populate[rating][populate][coPayment][populate][fields][1]=score&
      populate[rating][populate][coPayment][populate][fields][2]=maxScore&
      populate[rating][populate][waitingPeriods][populate][fields][0]=title&
      populate[rating][populate][waitingPeriods][populate][fields][1]=score&
      populate[rating][populate][waitingPeriods][populate][fields][2]=maxScore&
      populate[rating][populate][noClaimBonus][populate][fields][0]=title&
      populate[rating][populate][noClaimBonus][populate][fields][1]=score&
      populate[rating][populate][noClaimBonus][populate][fields][2]=maxScore
    `.replace(/\s+/g, "");

    const response = await strapiAPI.get(
      `/api/health-variants?${populateQuery}`
    );

    if (response.data.data && response.data.data.length > 0) {
      const variantData = response.data.data[0].attributes;
      console.log(JSON.stringify(variantData, null, 2));
      logger.info(`Successfully fetched data for variant: ${variantSlug}`);
      return variantData;
    } else {
      logger.warn(`No data found for variant: ${variantSlug}`);
      return null;
    }
  } catch (error) {
    logger.error(
      `Error fetching data for variant ${variantSlug}: ${error.message}`
    );
    return null;
  }
}

// async function generateNewId() {
//   try {
//     const response = await fetch(
//       "https://idgen.corp.non-prod.oneassure.in/generate",
//       {
//         headers: {
//           "sec-ch-ua":
//             '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
//           "sec-ch-ua-mobile": "?1",
//           "sec-ch-ua-platform": '"Android"',
//           Referer: "https://idgen.corp.non-prod.oneassure.in/",
//           "Referrer-Policy": "strict-origin-when-cross-origin",
//         },
//         body: null,
//         method: "GET",
//       }
//     );

//     if (!response.ok) {
//       throw new Error(`ID generation failed: ${response.statusText}`);
//     }

//     const data = await response.json();
//     return data.id;
//   } catch (error) {
//     logger.error(`❌ Failed to generate new ID: ${error.message}`);
//     throw error;
//   }
// }

// // Function to get variant ID from slug
// async function getVariantIdFromSlug(client, variantSlug) {
//   try {
//     const query = `SELECT id FROM health.product_variants WHERE variant_slug = $1`;
//     const result = await client.query(query, [variantSlug]);

//     if (result.rows.length > 0) {
//       return result.rows[0].id;
//     } else {
//       logger.warn(`Variant with slug '${variantSlug}' not found in database`);
//       return null;
//     }
//   } catch (error) {
//     logger.error(
//       `Error getting variant ID for slug ${variantSlug}: ${error.message}`
//     );
//     return null;
//   }
// }

// // Function to get related variant ID from slug
// async function getRelatedVariantId(client, relatedSlug) {
//   if (!relatedSlug) return null;

//   try {
//     const query = `SELECT id FROM health.product_variants WHERE variant_slug = $1`;
//     const result = await client.query(query, [relatedSlug]);
//     return result.rows.length > 0 ? result.rows[0].id : null;
//   } catch (error) {
//     logger.error(
//       `Error getting related variant ID for slug ${relatedSlug}: ${error.message}`
//     );
//     return null;
//   }
// }

// // Function to insert static content
// async function insertStaticContent(client, healthVariantId, data) {
//   try {
//     // Check if static content already exists
//     const checkQuery = `SELECT (id, exclusions, about_the_plan, hero_title, verdict) FROM site.health_variant_static_content WHERE health_variant_id = $1`;
//     const existingResult = await client.query(checkQuery, [healthVariantId]);
//     console.log(existingResult);
//     if (existingResult.rows.length > 0) {
//       logger.info(
//         `Static content already exists for variant ${healthVariantId}, skipping...`
//       );
//       const query = `UPDATE site.health_variant_static_content SET exclusions = $1, about_the_plan = $2, hero_title = $3, verdict = $4 WHERE health_variant_id = $5`;
//       const values = [
//         data.exclusions.map((exclusion) => exclusion.exclusion),
//         data.aboutThePlan,
//         data.hero?.title,
//         data?.verdict || "",
//         healthVariantId,
//       ];
//       await client.query(query, values);
//       logger.success(
//         `✅ Updated static content for variant ${healthVariantId}`
//       );
//       return true;
//     }

//     const query = `
//       INSERT INTO site.health_variant_static_content 
//       (id, health_variant_id, product_popularity, subtitle, comparison_enabled, specialty, best_for, decision_guide, exclusions, about_the_plan, hero_title, verdict, created_at, updated_at)
//       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW(), NOW())
//     `;

//     const values = [
//       await generateNewId(),
//       healthVariantId,
//       0, // default popularity
//       data.hero?.title || "",
//       false, // default comparison enabled
//       "", // default specialty
//       [], // default best_for array
//       [], // default decision_guide array
//       data.exclusions.map((exclusion) => exclusion.exclusion),
//       data.aboutThePlan,
//       data.hero?.title,
//       data?.verdict || "",
//     ];
//     console.log(query, values);
//     await client.query(query, values);
//     logger.success(`✅ Inserted static content for variant ${healthVariantId}`);
//     return true;
//   } catch (error) {
//     console.log(error);
//     logger.error(
//       `❌ Failed to insert static content for variant ${healthVariantId}: ${error.message}`
//     );
//     return false;
//   }
// }

// // Function to insert FAQs
// async function insertFAQs(client, healthVariantId, faqs) {
//   if (!faqs || !Array.isArray(faqs) || faqs.length === 0) {
//     logger.info(`No FAQs to insert for variant ${healthVariantId}`);
//     return true;
//   }

//   try {
//     // Clear existing FAQs
//     await client.query(
//       `DELETE FROM site.health_variant_faqs WHERE health_variant_id = $1`,
//       [healthVariantId]
//     );

//     for (const faq of faqs) {
//       const query = `
//         INSERT INTO site.health_variant_faqs 
//         (id, question, answer, health_variant_id, created_at, updated_at)
//         VALUES ($1, $2, $3, $4, NOW(), NOW())
//       `;

//       const values = [
//         await generateNewId(),
//         faq.question || "",
//         faq.ans || "",
//         healthVariantId,
//       ];

//       await client.query(query, values);
//     }

//     logger.success(
//       `✅ Inserted ${faqs.length} FAQs for variant ${healthVariantId}`
//     );
//     return true;
//   } catch (error) {
//     logger.error(
//       `❌ Failed to insert FAQs for variant ${healthVariantId}: ${error.message}`
//     );
//     return false;
//   }
// }

// // Function to insert WhyOneAssure items
// async function insertWhyOneAssure(client, healthVariantId, whyOneAssureItems) {
//   if (
//     !whyOneAssureItems ||
//     !Array.isArray(whyOneAssureItems) ||
//     whyOneAssureItems.length === 0
//   ) {
//     logger.info(
//       `No WhyOneAssure items to insert for variant ${healthVariantId}`
//     );
//     return true;
//   }

//   try {
//     // Clear existing items
//     await client.query(
//       `DELETE FROM site.health_variant_whyOneAssures WHERE health_variant_id = $1`,
//       [healthVariantId]
//     );

//     for (const item of whyOneAssureItems) {
//       const query = `
//         INSERT INTO site.health_variant_whyOneAssures 
//         (id, title, description, health_variant_id, created_at, updated_at)
//         VALUES ($1, $2, $3, $4, NOW(), NOW())
//       `;

//       const values = [
//         await generateNewId(),
//         item.title || "",
//         item.description || "",
//         healthVariantId,
//       ];

//       await client.query(query, values);
//     }

//     logger.success(
//       `✅ Inserted ${whyOneAssureItems.length} WhyOneAssure items for variant ${healthVariantId}`
//     );
//     return true;
//   } catch (error) {
//     logger.error(
//       `❌ Failed to insert WhyOneAssure items for variant ${healthVariantId}: ${error.message}`
//     );
//     return false;
//   }
// }

// // Function to insert Features
// // async function insertFeatures(client, healthVariantId, features) {
// //   if (!features || !Array.isArray(features) || features.length === 0) {
// //     logger.info(`No features to insert for variant ${healthVariantId}`);
// //     return true;
// //   }

// //   try {
// //     // Clear existing features
// //     await client.query(
// //       `DELETE FROM site.health_variant_features WHERE health_variant_id = $1`,
// //       [healthVariantId]
// //     );

// //     for (const feature of features) {
// //       const listedFeatures =
// //         feature.listedFeatures?.data?.map((f) => f.attributes?.feature || "") ||
// //         [];

// //       const query = `
// //         INSERT INTO site.health_variant_features
// //         (id, health_variant_id, title, description, listed_features, created_at, updated_at)
// //         VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
// //       `;

// //       const values = [
// //         await generateNewId(),
// //         healthVariantId,
// //         feature.title || "",
// //         feature.description || "",
// //         listedFeatures,
// //       ];

// //       await client.query(query, values);
// //     }

// //     logger.success(
// //       `✅ Inserted ${features.length} features for variant ${healthVariantId}`
// //     );
// //     return true;
// //   } catch (error) {
// //     logger.error(
// //       `❌ Failed to insert features for variant ${healthVariantId}: ${error.message}`
// //     );
// //     return false;
// //   }
// // }

// // Function to insert Ratings
// async function insertRatings(client, healthVariantId, rating) {
//   if (!rating) {
//     logger.info(`No ratings to insert for variant ${healthVariantId}`);
//     return true;
//   }

//   try {
//     // Clear existing ratings
//     await client.query(
//       `DELETE FROM site.health_variant_ratings WHERE health_variant_id = $1`,
//       [healthVariantId]
//     );

//     const ratingTypes = [
//       { key: "coverage", data: rating.coverage },
//       { key: "claimSettlement", data: rating.claimSettlement },
//       { key: "hospitalNetwork", data: rating.hospitalNetwork },
//       { key: "coPayment", data: rating.coPayment },
//       { key: "waitingPeriods", data: rating.waitingPeriods },
//       { key: "noClaimBonus", data: rating.noClaimBonus },
//     ];

//     for (const ratingType of ratingTypes) {
//       if (ratingType.data) {
//         const attrs = ratingType.data;

//         const query = `
//           INSERT INTO site.health_variant_ratings 
//           (id, health_variant_id, label, title, score, max_score, created_at, updated_at)
//           VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
//         `;

//         const values = [
//           await generateNewId(),
//           healthVariantId,
//           ratingType.key,
//           attrs.title || "",
//           parseFloat(attrs.score) || 0,
//           parseFloat(attrs.maxScore) || 10,
//         ];

//         await client.query(query, values);
//       }
//     }

//     logger.success(`✅ Inserted ratings for variant ${healthVariantId}`);
//     return true;
//   } catch (error) {
//     logger.error(
//       `❌ Failed to insert ratings for variant ${healthVariantId}: ${error.message}`
//     );
//     return false;
//   }
// }

// // Function to insert Policy Docs
// async function insertPolicyDocs(client, healthVariantId, policyDocs) {
//   if (!policyDocs || !Array.isArray(policyDocs) || policyDocs.length === 0) {
//     logger.info(`No policy docs to insert for variant ${healthVariantId}`);
//     return true;
//   }

//   try {
//     // Clear existing policy docs
//     await client.query(
//       `DELETE FROM site.health_variant_policy_docs WHERE health_variant_id = $1`,
//       [healthVariantId]
//     );

//     for (const doc of policyDocs) {
//       const documentUrl = doc.document?.data[0]?.attributes?.url || "";
//       console.log(documentUrl);

//       const query = `
//         INSERT INTO site.health_variant_policy_docs 
//         (id, health_variant_id, label, document_key, created_at, updated_at)
//         VALUES ($1, $2, $3, $4, NOW(), NOW())
//       `;

//       const values = [
//         await generateNewId(),
//         healthVariantId,
//         doc.label || "",
//         documentUrl,
//       ];

//       await client.query(query, values);
//     }

//     logger.success(
//       `✅ Inserted ${policyDocs.length} policy docs for variant ${healthVariantId}`
//     );
//     return true;
//   } catch (error) {
//     logger.error(
//       `❌ Failed to insert policy docs for variant ${healthVariantId}: ${error.message}`
//     );
//     return false;
//   }
// }

// // Function to insert Highlighted Features
// async function insertHighlightedFeatures(
//   client,
//   healthVariantId,
//   highlightedFeatures
// ) {
//   if (
//     !highlightedFeatures ||
//     !Array.isArray(highlightedFeatures) ||
//     highlightedFeatures.length === 0
//   ) {
//     logger.info(
//       `No highlighted features to insert for variant ${healthVariantId}`
//     );
//     return true;
//   }

//   try {
//     // Clear existing highlighted features
//     await client.query(
//       `DELETE FROM site.health_variant_highlighted_features WHERE health_variant_id = $1`,
//       [healthVariantId]
//     );

//     for (const feature of highlightedFeatures) {
//       const iconUrl = feature.icon?.data?.attributes?.url || "";

//       const query = `
//         INSERT INTO site.health_variant_highlighted_features 
//         (id, health_variant_id, title, description, icon_key, created_at, updated_at)
//         VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
//       `;

//       const values = [
//         await generateNewId(),
//         healthVariantId,
//         feature.title || "",
//         feature.description || "",
//         iconUrl,
//       ];

//       await client.query(query, values);
//     }

//     logger.success(
//       `✅ Inserted ${highlightedFeatures.length} highlighted features for variant ${healthVariantId}`
//     );
//     return true;
//   } catch (error) {
//     logger.error(
//       `❌ Failed to insert highlighted features for variant ${healthVariantId}: ${error.message}`
//     );
//     return false;
//   }
// }

// // Function to insert Related Variants
// async function insertRelatedVariants(client, healthVariantId, variants) {
//   if (!variants || !Array.isArray(variants) || variants.length === 0) {
//     logger.info(`No related variants to insert for variant ${healthVariantId}`);
//     return true;
//   }

//   try {
//     // Clear existing related variants
//     await client.query(
//       `DELETE FROM site.health_variant_related_variants WHERE health_variant_id = $1`,
//       [healthVariantId]
//     );

//     for (const variant of variants) {
//       const relatedVariant = variant.relatedVariant?.data?.attributes;
//       if (relatedVariant?.slug) {
//         const relatedVariantId = await getRelatedVariantId(
//           client,
//           relatedVariant.slug
//         );
//         if (relatedVariantId) {
//           const features =
//             variant.features?.data?.map((f) => f.attributes?.feature || "") ||
//             [];

//           const query = `
//             INSERT INTO site.health_variant_related_variants 
//             (id, health_variant_id, related_variant_id, features, created_at, updated_at)
//             VALUES ($1, $2, $3, $4, NOW(), NOW())
//           `;

//           const values = [
//             await generateNewId(),
//             healthVariantId,
//             relatedVariantId,
//             features,
//           ];

//           await client.query(query, values);
//         }
//       }
//     }

//     logger.success(
//       `✅ Inserted related variants for variant ${healthVariantId}`
//     );
//     return true;
//   } catch (error) {
//     logger.error(
//       `❌ Failed to insert related variants for variant ${healthVariantId}: ${error.message}`
//     );
//     return false;
//   }
// }

// // Function to insert SEO content
// async function insertSEOContent(client, healthVariantId, seo) {
//   if (!seo) {
//     logger.info(`No SEO content to insert for variant ${healthVariantId}`);
//     return true;
//   }

//   try {
//     // Check if SEO content already exists
//     const checkQuery = `SELECT id FROM site.health_variant_seo WHERE health_variant_id = $1`;
//     const existingResult = await client.query(checkQuery, [healthVariantId]);

//     if (existingResult.rows.length > 0) {
//       // Update existing SEO content
//       const updateQuery = `
//         UPDATE site.health_variant_seo 
//         SET meta_title = $1, meta_description = $2, meta_keyword = $3, prevent_indexing = $4, source = $5, updated_at = NOW()
//         WHERE health_variant_id = $6
//       `;

//       const values = [
//         seo.metaTitle || "",
//         seo.metaDescription || "",
//         seo.keyword || "",
//         seo.preventIndexing || false,
//         seo.source || "none",
//         healthVariantId,
//       ];

//       await client.query(updateQuery, values);
//       logger.success(`✅ Updated SEO content for variant ${healthVariantId}`);
//     } else {
//       // Insert new SEO content
//       const insertQuery = `
//         INSERT INTO site.health_variant_seo 
//         (id, health_variant_id, meta_title, meta_description, meta_keyword, prevent_indexing, source, created_at, updated_at)
//         VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
//       `;

//       const values = [
//         await generateNewId(),
//         healthVariantId,
//         seo.metaTitle || "",
//         seo.metaDescription || "",
//         seo.keyword || "",
//         seo.preventIndexing || false,
//         seo.source || "none",
//       ];

//       await client.query(insertQuery, values);
//       logger.success(`✅ Inserted SEO content for variant ${healthVariantId}`);
//     }

//     return true;
//   } catch (error) {
//     logger.error(
//       `❌ Failed to insert SEO content for variant ${healthVariantId}: ${error.message}`
//     );
//     return false;
//   }
// }

// // Main function to migrate variant data
// async function migrateVariantData(client, variantSlug) {
//   try {
//     logger.info(`🚀 Starting migration for variant: ${variantSlug}`);

//     // Get variant ID from slug
//     const healthVariantId = await getVariantIdFromSlug(client, variantSlug);
//     if (!healthVariantId) {
//       logger.error(`❌ Could not find variant ID for slug: ${variantSlug}`);
//       return false;
//     }

//     // Fetch complete data from Strapi
//     const data = await fetchCompleteVariantData(variantSlug);
//     if (!data) {
//       logger.error(`❌ Could not fetch data for variant: ${variantSlug}`);
//       return false;
//     }

//     // Begin transaction
//     await client.query("BEGIN");

//     try {
//       // Insert data into various tables
//       await insertStaticContent(client, healthVariantId, data);
//       await insertFAQs(client, healthVariantId, data.faqs);
//       await insertWhyOneAssure(client, healthVariantId, data.whyOneAssure);
//       // await insertFeatures(client, healthVariantId, data.features);
//       await insertRatings(client, healthVariantId, data.rating);
//       await insertPolicyDocs(client, healthVariantId, data.policyDocs);
//       await insertHighlightedFeatures(
//         client,
//         healthVariantId,
//         data.highlightedFeatures
//       );
//       await insertRelatedVariants(client, healthVariantId, data.variants);
//       await insertSEOContent(client, healthVariantId, data.seo);

//       // Commit transaction
//       await client.query("COMMIT");
//       logger.success(
//         `✅ Successfully migrated all data for variant: ${variantSlug}`
//       );
//       return true;
//     } catch (error) {
//       // Rollback transaction on error
//       await client.query("ROLLBACK");
//       throw error;
//     }
//   } catch (error) {
//     logger.error(
//       `❌ Failed to migrate variant ${variantSlug}: ${error.message}`
//     );
//     return false;
//   }
// }

// // Function to migrate specific variant by slug
// async function migrateSpecificVariant(variantSlug) {
//   const client = await pool.connect();

//   try {
//     logger.info(`🎯 Migrating specific variant: ${variantSlug}`);
//     const success = await migrateVariantData(client, variantSlug);

//     if (success) {
//       logger.success(`✅ Successfully migrated variant: ${variantSlug}`);
//     } else {
//       logger.error(`❌ Failed to migrate variant: ${variantSlug}`);
//     }
//   } catch (error) {
//     logger.error(`❌ Error migrating variant ${variantSlug}: ${error.message}`);
//   } finally {
//     client.release();
//   }
// }

// // Function to migrate all variants
// async function migrateAllVariants() {
//   const client = await pool.connect();
//   const variants = await client.query(
//     `SELECT variant_slug FROM health.product_variants WHERE variant_slug IS NOT NULL`
//   );
//   for (const variant of variants.rows) {
//     await migrateVariantData(client, variant.variant_slug);
//   }
// }
// // Function to validate environment variables
// function validateEnvironment() {
//   const requiredVars = [
//     "STRAPI_BASE_URL",
//     "TOKEN",
//     "DB_USER",
//     "DB_HOST",
//     "DB_NAME",
//     "DB_PASSWORD",
//   ];

//   const missing = requiredVars.filter((varName) => !process.env[varName]);

//   if (missing.length > 0) {
//     logger.error("❌ Missing required environment variables:");
//     missing.forEach((varName) => logger.error(`   - ${varName}`));
//     process.exit(1);
//   }
// }

// // Function to test database connection
// async function testDatabaseConnection() {
//   try {
//     const client = await pool.connect();

//     // Test connection to the health.product_variants table
//     const testQuery = `SELECT COUNT(*) FROM health.product_variants WHERE variant_slug IS NOT NULL`;
//     const result = await client.query(testQuery);

//     logger.success("✅ Database connection successful");
//     logger.info(
//       `📊 Found ${result.rows[0].count} variants with slugs in database`
//     );

//     client.release();
//   } catch (error) {
//     console.log(error);
//     logger.error("❌ Database connection failed:", error);
//     process.exit(1);
//   }
// }

// // Main function
// async function main() {
//   logger.info("🏥 Variant Data Migration Tool");
//   logger.info("=====================================");
//   logger.info(`Migration started at: ${new Date().toISOString()}`);
//   logger.info(`Log file: ${logFile}`);
//   logger.info(`Strapi Base URL: ${STRAPI_BASE_URL}`);
//   logger.info(`Database Host: ${dbConfig.host}`);
//   logger.info(`Database Name: ${dbConfig.database}`);
//   logger.info("=====================================");

//   validateEnvironment();
//   await testDatabaseConnection();

//   // Get variant slug from command line argument
//   const variantSlug = process.argv[2];

//   if (!variantSlug) {
//     logger.info(`🎯 Migrating data for all variants`);
//     await migrateAllVariants();
//   } else {
//     logger.info(`🎯 Migrating data for variant slug: ${variantSlug}`);
//     await migrateSpecificVariant(variantSlug);
//   }

//   // Write final summary to log
//   const endTime = new Date().toISOString();
//   logger.info("=====================================");
//   logger.info("MIGRATION COMPLETED");
//   logger.info(`End time: ${endTime}`);
//   logger.info(`Log file saved to: ${logFile}`);
//   logger.info("=====================================");
// }

// // Handle graceful shutdown
// process.on("SIGINT", () => {
//   logger.warn("\n⚠️  Migration interrupted by user");
//   pool.end();
//   process.exit(0);
// });

// process.on("unhandledRejection", (error) => {
//   logger.error("❌ Unhandled promise rejection:", error);
//   pool.end();
//   process.exit(1);
// });

// // Close pool on exit
// process.on("exit", () => {
//   pool.end();
// });

// // Run the migration
// main().catch((error) => {
//   logger.error("❌ Migration failed:", error);
//   pool.end();
//   process.exit(1);
// });
