[2025-09-01T13:11:03.931Z] [INFO] 🎯 Migrating specific variant: aditya-birla-health-insurance
[2025-09-01T13:11:03.932Z] [INFO] 🚀 Starting migration for insurer: aditya-birla-health-insurance
[2025-09-01T13:11:03.934Z] [SUCCESS] ✅ Database connection successful
[2025-09-01T13:11:03.935Z] [INFO] 📊 Found 24 insurers with slugs in database
[2025-09-01T13:11:03.936Z] [INFO] Fetching complete data for insurer: aditya-birla-health-insurance
[2025-09-01T13:11:04.286Z] [INFO] Successfully fetched data for insurer: aditya-birla-health-insurance
[2025-09-01T13:11:04.292Z] [INFO] Static content already exists for insurer 18Z6l2lLbbEJ5kf3, skipping...
[2025-09-01T13:11:04.295Z] [SUCCESS] ✅ Updated static content for insurer 18Z6l2lLbbEJ5kf3
[2025-09-01T13:11:04.608Z] [SUCCESS] ✅ Inserted 5 FAQs for insurer 18Z6l2lLbbEJ5kf3
[2025-09-01T13:11:04.614Z] [ERROR] ❌ Failed to insert claim settlement for insurer 18Z6l2lLbbEJ5kf3: update or delete on table "insurer_claim_settlement" violates foreign key constraint "insurer_claim_settlement_type_insurer_claim_settlement_id_fkey" on table "insurer_claim_settlement_type"
[2025-09-01T13:11:04.617Z] [ERROR] ❌ Failed to insert SEO content for insurer 18Z6l2lLbbEJ5kf3: current transaction is aborted, commands ignored until end of transaction block
[2025-09-01T13:11:04.618Z] [SUCCESS] ✅ Successfully migrated all data for insurer: aditya-birla-health-insurance
[2025-09-01T13:11:04.618Z] [SUCCESS] ✅ Successfully migrated variant: aditya-birla-health-insurance
